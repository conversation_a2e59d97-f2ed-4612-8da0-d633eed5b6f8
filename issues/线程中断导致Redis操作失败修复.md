# 线程中断导致Redis操作失败修复

## 问题描述
WebSocket重连过程中，在`solana-reconnect-executor`守护线程执行订阅操作时，出现`InterruptedException`异常，导致Redis操作失败并传播影响整个重连流程。

## 关键错误日志
```
2025-06-19 13:21:45 [solana-reconnect-executor] ERROR o.d.sol.service.SolanaMonitorService
 - 订阅地址geAqjvoGZ4fDgCbz7NgVgwh5KhQazKabyD3xu15uXYV失败
org.redisson.client.RedisException: java.lang.InterruptedException
	at org.redisson.command.CommandAsyncService.get(CommandAsyncService.java:180)
```

## 根因分析
1. **守护线程风险**：重连执行器设置为守护线程，在执行重要业务操作时容易被JVM中断
2. **Redis依赖过重**：订阅状态管理过度依赖Redis，网络IO阻塞影响核心流程
3. **异常传播链**：`InterruptedException` → `RedisException` → 订阅失败 → 连接状态异常

## 解决方案：内存优先架构

### 阶段1：纯内存化架构 ⭐
**核心策略**：将订阅状态管理从Redis迁移到内存，消除网络IO阻塞和线程中断问题

**实现要点**：
- 使用`ConcurrentHashMap`管理订阅状态
- 移除重连线程中的Redis操作
- 简化异常处理，提高稳定性

### 阶段2：测试验证
- 重连机制稳定性测试
- 订阅状态一致性验证
- 异常恢复能力测试

### 阶段3：可选持久化（待验证通过）
- Redis仅作为备份存储
- 异步同步，不影响主流程

## 预期效果
- ✅ 消除线程中断导致的订阅失败
- ✅ 提升重连速度和稳定性  
- ✅ 降低外部依赖对核心功能的影响
- ✅ 简化异常处理逻辑

## 执行状态
- [x] 方案设计和计划制定
- [ ] 🔄 内存状态管理实现
- [ ] 🔄 Redis操作移除/简化
- [ ] 🔄 异常处理优化
- [ ] 编译和基本测试
- [ ] 完整功能测试
- [ ] 生产环境验证 