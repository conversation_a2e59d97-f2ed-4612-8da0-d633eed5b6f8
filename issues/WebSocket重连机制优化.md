# WebSocket重连机制优化

## 🐛 问题背景
用户反馈连续断开连接后，WebSocket重连机制存在以下问题：
1. SSL握手失败导致ERROR状态，无法触发重连
2. 重连次数达到上限后完全停止重连
3. 重连后等待连接建立超时
4. 错误状态和正常断开状态处理不统一

## 📊 问题日志分析
```
2025-06-19 11:34:58 [WebSocketConnectReadThread-150] ERROR o.d.sol.service.SolanaMonitorService
 - WebSocket发生错误
javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
```

```
2025-06-19 11:35:48 [solana-reconnect-2] WARN  o.d.sol.service.SolanaMonitorService
 - 重连后等待连接建立超时
```

## 🔧 优化方案

### 1. 统一错误处理机制
**问题**：ERROR状态和DISCONNECTED状态重连逻辑不统一

**解决方案**：
- 创建统一的`handleReconnectionLogic()`方法
- ERROR和DISCONNECTED状态都触发重连逻辑
- 支持SSL握手失败等错误状态的重连

### 2. 重连计数器重置机制
**问题**：达到最大重连次数后永久停止重连

**解决方案**：
- 增加`scheduleReconnectReset()`方法
- 达到最大重连次数后，等待5分钟自动重置计数器
- 重置后立即尝试重新连接

### 3. 改进重连逻辑
**问题**：重连失败后不继续尝试下一次重连

**解决方案**：
- 重连失败后递归调用`scheduleReconnect()`
- 清理旧的WebSocket连接避免冲突
- 增加更详细的错误日志和状态跟踪

### 4. 优化连接等待机制
**问题**：重连后等待连接建立经常超时

**解决方案**：
- 创建`waitForConnectionWithRetry()`专用于重连场景
- 重连时使用2倍超时时间（60秒）
- 增加ERROR状态检测，及时终止等待
- 更详细的连接状态日志

### 5. 调整配置参数
**优化前**：
- maxReconnectAttempts: 5
- reconnectInterval: 10秒

**优化后**：
- maxReconnectAttempts: 10（增加重连次数）
- reconnectInterval: 5秒（减少基础间隔，使用指数退避）

## 💡 核心改进点

### 错误处理统一化
```java
@Override
public void onError(Exception ex) {
    log.error("WebSocket发生错误", ex);
    connectionStateManager.setState(MonitorState.ERROR);
    // 错误状态也需要触发重连
    handleReconnectionLogic("连接错误");
}
```

### 重连计数器自动重置
```java
private void scheduleReconnectReset() {
    new Thread(() -> {
        try {
            TimeUnit.MINUTES.sleep(5); // 等待5分钟
            int resetAttempts = reconnectAttempts.getAndSet(0);
            log.info("重连计数器已重置，原值: {}，开始新的重连周期", resetAttempts);
            
            if (connectionStateManager.getCurrentState().needsReconnect()) {
                scheduleReconnect();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }, "reconnect-reset").start();
}
```

### 持续重连机制
```java
// 重连失败，检查是否需要继续尝试
if (currentAttempt < monitorConfig.getMaxReconnectAttempts()) {
    log.info("第{}次重连失败，将继续尝试下一次重连", currentAttempt);
    scheduleReconnect(); // 递归调用继续重连
} else {
    log.error("重连尝试已达到最大次数，进入等待重置状态");
    scheduleReconnectReset();
}
```

## 🎯 预期效果

1. **健壮性提升**：SSL握手失败等网络错误不再导致重连停止
2. **持续可用性**：重连计数器自动重置，确保长期稳定运行
3. **快速恢复**：优化的退避策略和超时设置提高重连成功率
4. **问题诊断**：更详细的日志帮助快速定位连接问题

## 📈 监控指标

优化后可以通过以下指标评估重连效果：
- 重连成功率
- 平均重连耗时
- 连接稳定性（连接持续时间）
- 错误类型分布

## ✅ 验证方法

1. **模拟网络中断**：测试自动重连是否正常工作
2. **SSL握手失败**：验证错误状态下的重连逻辑
3. **长期运行测试**：验证重连计数器重置机制
4. **连接稳定性测试**：检查重连后的订阅状态一致性 