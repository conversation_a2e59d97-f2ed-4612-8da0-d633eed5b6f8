# 统一多链配置架构重构

## 任务背景
用户需要支持多链配置，当前WalletConfig只包含TRON配置，需要设计统一但简洁的多链配置架构。

## 执行计划
1. 重构WalletConfig为统一多链配置入口
2. 保持现有TRON配置结构不变
3. 为未来其他链预留简洁扩展空间
4. 确保配置注入正常工作
5. 编写单元测试验证

## 设计原则
- 保持简洁，避免过度抽象
- 统一管理但职责分离
- 易于扩展新链配置
- 兼容现有代码

## 配置示例

### application.yml 配置
```yaml
wallet:
  tron:
    # 测试网配置
    apiUrl: "https://api.shasta.trongrid.io"
    scanUrl: "https://shastapi.tronscan.org"
    burnEnergyLimit: 100000
    turnMainNetwork: false
    
    # 主网配置
    mainnetApiUrl: "https://api.trongrid.io"
    mainnetScanUrl: "https://apilist.tronscanapi.com"
    apiKeyList:
      - "your-api-key-1"
      - "your-api-key-2"

  # 未来扩展示例：
  # ethereum:
  #   rpcUrl: "https://mainnet.infura.io/v3/YOUR-PROJECT-ID"
  #   scanUrl: "https://api.etherscan.io"
  #   useMainnet: true
  #   gasLimit: 21000
```

## 实现特点
- ✅ 统一配置入口 WalletConfig
- ✅ 保持现有TRON功能完全兼容
- ✅ 简洁的多链扩展架构
- ✅ 良好的代码文档和注释
- ✅ 预留其他链配置位置

## 执行状态
- [x] 计划制定
- [x] 配置类重构完成
- [x] 兼容性验证
- [x] 单元测试创建
- [ ] 完成确认 