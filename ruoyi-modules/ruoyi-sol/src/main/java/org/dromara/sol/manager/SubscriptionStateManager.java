package org.dromara.sol.manager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.constants.SolRedisKeyConstants;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 订阅状态管理器
 * 专门管理WebSocket订阅相关的状态
 * 
 * 设计原则：
 * 1. 内存缓存提供快速访问
 * 2. Redis提供持久化和故障恢复
 * 3. 定期同步确保数据一致性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubscriptionStateManager {

    private final RedissonClient redissonClient;
    
    // 内存缓存：订阅ID -> 地址映射（快速查找）
    private final Map<String, String> subscriptionCache = new ConcurrentHashMap<>();
    
    // 内存缓存：地址 -> 订阅ID映射（反向查找）
    private final Map<String, String> addressToSubscriptionCache = new ConcurrentHashMap<>();
    
    /**
     * 保存订阅映射
     * 同时更新内存缓存和Redis
     */
    public boolean saveSubscriptionMapping(String subscriptionId, String address) {
        if (subscriptionId == null || address == null) {
            log.warn("保存订阅映射失败：订阅ID或地址为空");
            return false;
        }
        
        try {
            // 1. 更新Redis（持久化）
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            redisMapping.put(subscriptionId, address);
            
            // 2. 更新内存缓存（快速访问）
            subscriptionCache.put(subscriptionId, address);
            addressToSubscriptionCache.put(address, subscriptionId);
            
            log.debug("保存订阅映射成功：订阅ID={}, 地址={}", subscriptionId, address);
            return true;
            
        } catch (Exception e) {
            log.error("保存订阅映射失败：订阅ID={}, 地址={}, 错误: {}", subscriptionId, address, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 根据订阅ID获取地址（优先从内存缓存）
     */
    public String getAddressBySubscription(String subscriptionId) {
        if (subscriptionId == null) {
            return null;
        }
        
        // 1. 先从内存缓存获取
        String address = subscriptionCache.get(subscriptionId);
        if (address != null) {
            return address;
        }
        
        // 2. 从Redis获取并更新缓存
        try {
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            address = redisMapping.get(subscriptionId);
            
            if (address != null) {
                // 更新内存缓存
                subscriptionCache.put(subscriptionId, address);
                addressToSubscriptionCache.put(address, subscriptionId);
            }
            
            return address;
            
        } catch (Exception e) {
            log.error("从Redis获取订阅映射失败：订阅ID={}, 错误: {}", subscriptionId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 根据地址获取订阅ID（优先从内存缓存）
     */
    public String getSubscriptionByAddress(String address) {
        if (address == null) {
            return null;
        }
        
        // 1. 先从内存缓存获取
        String subscriptionId = addressToSubscriptionCache.get(address);
        if (subscriptionId != null) {
            return subscriptionId;
        }
        
        // 2. 从Redis查找（需要遍历，性能较低）
        try {
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            for (Map.Entry<String, String> entry : redisMapping.entrySet()) {
                if (address.equals(entry.getValue())) {
                    subscriptionId = entry.getKey();
                    // 更新内存缓存
                    subscriptionCache.put(subscriptionId, address);
                    addressToSubscriptionCache.put(address, subscriptionId);
                    return subscriptionId;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("从Redis查找订阅ID失败：地址={}, 错误: {}", address, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 移除订阅映射
     */
    public boolean removeSubscriptionMapping(String subscriptionId) {
        if (subscriptionId == null) {
            return false;
        }
        
        try {
            // 1. 从内存缓存获取地址
            String address = subscriptionCache.get(subscriptionId);
            
            // 2. 从Redis移除
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            String removedAddress = redisMapping.remove(subscriptionId);
            
            // 3. 从内存缓存移除
            subscriptionCache.remove(subscriptionId);
            if (address != null) {
                addressToSubscriptionCache.remove(address);
            }
            if (removedAddress != null && !removedAddress.equals(address)) {
                addressToSubscriptionCache.remove(removedAddress);
            }
            
            log.debug("移除订阅映射成功：订阅ID={}, 地址={}", subscriptionId, address);
            return true;
            
        } catch (Exception e) {
            log.error("移除订阅映射失败：订阅ID={}, 错误: {}", subscriptionId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 从Redis同步所有订阅映射到内存缓存
     */
    public void syncFromRedis() {
        try {
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            Map<String, String> allMappings = new HashMap<>(redisMapping);
            
            // 清空并重建内存缓存
            subscriptionCache.clear();
            addressToSubscriptionCache.clear();
            
            for (Map.Entry<String, String> entry : allMappings.entrySet()) {
                String subscriptionId = entry.getKey();
                String address = entry.getValue();
                
                subscriptionCache.put(subscriptionId, address);
                addressToSubscriptionCache.put(address, subscriptionId);
            }
            
            log.info("从Redis同步{}个订阅映射到内存缓存", allMappings.size());
            
        } catch (Exception e) {
            log.error("从Redis同步订阅映射失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清空所有订阅映射
     */
    public void clearAllMappings() {
        try {
            // 清空Redis
            RMap<String, String> redisMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            redisMapping.clear();
            
            // 清空内存缓存
            subscriptionCache.clear();
            addressToSubscriptionCache.clear();
            
            log.info("已清空所有订阅映射");
            
        } catch (Exception e) {
            log.error("清空订阅映射失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取所有订阅的地址
     */
    public Set<String> getAllSubscribedAddresses() {
        return Set.copyOf(addressToSubscriptionCache.keySet());
    }
    
    /**
     * 获取所有订阅ID
     */
    public Set<String> getAllSubscriptionIds() {
        return Set.copyOf(subscriptionCache.keySet());
    }
    
    /**
     * 获取订阅映射数量
     */
    public int getSubscriptionCount() {
        return subscriptionCache.size();
    }
    
    /**
     * 检查地址是否已订阅
     */
    public boolean isAddressSubscribed(String address) {
        return addressToSubscriptionCache.containsKey(address);
    }
    
    /**
     * 检查订阅ID是否存在
     */
    public boolean isSubscriptionExists(String subscriptionId) {
        return subscriptionCache.containsKey(subscriptionId);
    }
    
    /**
     * 获取订阅状态摘要
     */
    public SubscriptionStateSummary getStateSummary() {
        return SubscriptionStateSummary.builder()
            .totalSubscriptions(getSubscriptionCount())
            .subscribedAddresses(getAllSubscribedAddresses().size())
            .cacheHitRate(calculateCacheHitRate())
            .build();
    }
    
    /**
     * 计算缓存命中率（简化实现）
     */
    private double calculateCacheHitRate() {
        // 这里可以实现更复杂的缓存命中率统计
        return subscriptionCache.isEmpty() ? 0.0 : 1.0;
    }
    
    /**
     * 订阅状态摘要
     */
    @lombok.Builder
    @lombok.Data
    public static class SubscriptionStateSummary {
        private int totalSubscriptions;
        private int subscribedAddresses;
        private double cacheHitRate;
    }
}
