package org.dromara.sol.manager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.constants.SolRedisKeyConstants;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.redisson.api.RList;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Solana监控地址管理器
 * 负责管理Solana地址的监控状态，包括地址的添加、状态更新、重试等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolMonitorManager {

    private final RedissonClient redissonClient;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    /**
     * 添加监控地址
     * 1. 将地址添加到监控列表
     * 2. 设置初始状态为PENDING
     * 3. 保存地址详细信息
     *
     * @param wallet 钱包信息
     * @return 是否添加成功
     */
    public boolean addMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        try {
            String usdtAddress = wallet.getCstUsdtAddress();
            String usdcAddress = wallet.getCstUsdcAddress();

            //保存主地址
            // 添加到监控地址哈希表，设置合理超时
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);

            // 使用快速超时，避免在同步架构下长时间阻塞
            boolean mainResult = executeWithTimeout(() -> {
                return monitorAddresses.putIfAbsent(wallet.getCstAddress(), SolRedisKeyConstants.STATUS_PENDING) == null;
            }, 2000); // 2秒超时

            if (mainResult) {
                saveAddressDetail(wallet.getCstAddress(), wallet, SolCoinType.SOL.getCode());
            }

            // 保存USDT地址状态
            if (usdtAddress != null && !usdtAddress.isEmpty()) {
                boolean usdtResult = executeWithTimeout(() -> {
                    return monitorAddresses.putIfAbsent(usdtAddress, SolRedisKeyConstants.STATUS_PENDING) == null;
                }, 2000);

                if (usdtResult) {
                    saveAddressDetail(usdtAddress, wallet, SolCoinType.USDT.getCode());
                }
            }

            // 保存USDC地址状态
            if (usdcAddress != null && !usdcAddress.isEmpty()) {
                boolean usdcResult = executeWithTimeout(() -> {
                    return monitorAddresses.putIfAbsent(usdcAddress, SolRedisKeyConstants.STATUS_PENDING) == null;
                }, 2000);

                if (usdcResult) {
                    saveAddressDetail(usdcAddress, wallet, SolCoinType.USDC.getCode());
                }
            }

            return true;

        } catch (Exception e) {
            // 深度检查异常链，寻找InterruptedException
            Throwable cause = e;
            while (cause != null) {
                if (cause instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // 保留中断状态
                    log.warn("添加监控地址时线程被中断，钱包ID: {}", wallet.getId());
                    return false;
                }
                cause = cause.getCause();
            }

            log.error("添加监控地址失败，钱包ID: {}: {}", wallet.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 带超时的Redis操作执行器
     * 防止在同步架构下长时间阻塞重连线程
     */
    private <T> T executeWithTimeout(RedisOperation<T> operation, long timeoutMs) {
        try {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    return operation.execute();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.warn("Redis操作超时，超时时间: {}ms", timeoutMs);
            throw new RuntimeException("Redis操作超时", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Redis操作被中断", e);
        } catch (Exception e) {
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * Redis操作接口
     */
    @FunctionalInterface
    private interface RedisOperation<T> {
        T execute() throws Exception;
    }

    /**
     * 保存地址详细信息
     *
     * @param address  地址
     * @param wallet   钱包信息
     * @param coinType 币种类型 (USDT/USDC)
     */
    private void saveAddressDetail(String address, MetaSolanaCstaddressinfoVo wallet, String coinType) {
        String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;

        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("id", String.valueOf(wallet.getId()));
        detailMap.put("address", address);
        detailMap.put("mainAddress", wallet.getCstAddress());
        detailMap.put("coinType", coinType);
        detailMap.put("status", SolRedisKeyConstants.STATUS_PENDING);
        detailMap.put("lastUpdateTime", LocalDateTime.now().toString());

        // 保存地址详细信息
        RMap<String, String> addressDetail = redissonClient.getMap(detailKey);
        addressDetail.putAll(detailMap);

        // 设置过期时间，比如30天
        addressDetail.expire(Duration.ofDays(30));
    }

    /**
     * 更新地址监控状态
     *
     * @param address 地址
     * @param status  状态
     * @return 是否更新成功
     */
    public boolean updateAddressStatus(String address, String status) {
        try {
            // 更新监控地址状态
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
            monitorAddresses.put(address, status);

            // 更新地址详细信息
            String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
            RMap<String, String> addressDetail = redissonClient.getMap(detailKey);

            if (addressDetail.isExists()) {
                addressDetail.put("status", status);
                addressDetail.put("lastUpdateTime", LocalDateTime.now().toString());
            }

            // 如果状态为FAILED，则添加到失败队列
            if (SolRedisKeyConstants.STATUS_FAILED.equals(status)) {
                RList<String> failedQueue = redissonClient.getList(SolRedisKeyConstants.SOL_MONITOR_FAILED_QUEUE);
                if (!failedQueue.contains(address)) {
                    failedQueue.add(address);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("更新地址状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重置所有监控状态
     * 系统重启时调用，清空Redis并从数据库重新加载所有监控地址
     *
     * @return 重建的地址数量
     */
    public int resetAllMonitoringStatus() {
        try {
            log.info("系统重启，开始清空Redis并从数据库重新加载监控地址数据");

            // 1. 完全清空Redis中的所有监控相关数据
            clearAllRedisMonitorData();

            // 2. 从数据库重新加载所有钱包数据到Redis
            int rebuiltCount = rebuildRedisFromDatabase();

            log.info("监控数据重建完成，共重建{}个地址的监控信息", rebuiltCount);
            return rebuiltCount;

        } catch (Exception e) {
            log.error("重建监控数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 清空Redis中的所有监控相关数据
     */
    private void clearAllRedisMonitorData() {
        try {
            // 清空监控地址列表
            redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES).clear();

            // 清空失败队列
            redissonClient.getList(SolRedisKeyConstants.SOL_MONITOR_FAILED_QUEUE).clear();

            // 清空订阅映射
            redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING).clear();

            // 清空所有地址详情信息（通过模式匹配删除）
            String pattern = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + "*";
            redissonClient.getKeys().deleteByPattern(pattern);

            log.info("Redis监控数据清空完成");

        } catch (Exception e) {
            log.error("清空Redis监控数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从数据库重新构建Redis监控数据
     *
     * @return 重建的地址数量
     */
    private int rebuildRedisFromDatabase() {
        int count = 0;
        try {
            // 从数据库获取所有钱包信息
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();
            log.info("从数据库获取到{}个钱包记录", allWallets.size());

            // 批量重建Redis数据
            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                try {
                    // 使用现有的addMonitorAddress方法重建数据
                    boolean success = addMonitorAddress(wallet);
                    if (success) {
                        // 计算实际添加的地址数量
                        count += countWalletAddresses(wallet);
                    }
                } catch (Exception e) {
                    log.error("重建钱包{}的监控数据失败: {}", wallet.getId(), e.getMessage());
                }
            }

            return count;

        } catch (Exception e) {
            log.error("从数据库重建Redis数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 计算钱包包含的地址数量
     */
    private int countWalletAddresses(MetaSolanaCstaddressinfoVo wallet) {
        int count = 0;

        // 主地址
        if (wallet.getCstAddress() != null && !wallet.getCstAddress().isEmpty()) {
            count++;
        }

        // USDT地址
        if (wallet.getCstUsdtAddress() != null && !wallet.getCstUsdtAddress().isEmpty()) {
            count++;
        }

        // USDC地址
        if (wallet.getCstUsdcAddress() != null && !wallet.getCstUsdcAddress().isEmpty()) {
            count++;
        }

        return count;
    }

    /**
     * 获取所有需要监控的地址
     *
     * @return 地址集合
     */
    public Set<String> getAllMonitorAddresses() {
        try {
            // 获取监控地址哈希表
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
            return new HashSet<>(monitorAddresses.keySet());
        } catch (Exception e) {
            log.error("获取监控地址失败: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取指定状态的地址集合
     *
     * @param status 状态
     * @return 地址集合
     */
    public Set<String> getAddressesByStatus(String status) {
        Set<String> result = new HashSet<>();
        try {
            // 获取监控地址哈希表
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);

            // 遍历所有地址，筛选指定状态的地址
            for (Map.Entry<String, String> entry : monitorAddresses.entrySet()) {
                if (status.equals(entry.getValue())) {
                    result.add(entry.getKey());
                }
            }

            return result;
        } catch (Exception e) {
            log.error("获取指定状态的地址失败: {}", e.getMessage(), e);
            return result;
        }
    }

    /**
     * 根据地址获取详细信息
     *
     * @param address 地址
     * @return 详细信息
     */
    public Map<String, String> getAddressDetail(String address) {
        try {
            String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
            RMap<String, String> addressDetail = redissonClient.getMap(detailKey);

            if (addressDetail.isExists()) {
                return new HashMap<>(addressDetail.readAllMap());
            } else {
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("获取地址详情失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

        /**
     * 获取地址详细信息，如果不存在则记录警告
     * 系统启动时已经从数据库重建了所有数据，理论上不应该出现缺失
     *
     * @param address 地址
     * @return 详细信息
     */
    public Map<String, String> getOrRepairAddressDetail(String address) {
        try {
            // 从Redis获取地址详情
            Map<String, String> detail = getAddressDetail(address);

            if (!detail.isEmpty()) {
                return detail;
            }

            // 如果Redis中不存在，说明数据可能有问题
            log.warn("地址{}的详情信息在Redis中不存在，可能数据不同步或地址已被清理", address);

            // 可选：触发单个地址的快速修复（避免影响主流程）
            repairSingleAddressFromDatabase(address);

            // 重新尝试获取一次
            detail = getAddressDetail(address);
            if (!detail.isEmpty()) {
                log.info("地址{}的详情信息已快速修复", address);
                return detail;
            }

            log.warn("地址{}在当前数据库中不存在，跳过处理", address);
            return new HashMap<>();

        } catch (Exception e) {
            log.error("获取地址{}详情失败: {}", address, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 快速修复单个地址的详情信息
     * 仅查询该地址对应的钱包记录，避免全量查询
     *
     * @param address 需要修复的地址
     * @return 是否修复成功
     */
    public boolean repairSingleAddressFromDatabase(String address) {
        try {
            // 先尝试通过地址查询钱包（这里需要根据实际的查询方法调整）
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();

            // 查找包含该地址的钱包
            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                boolean isTargetWallet = false;
                String coinType = null;

                // 检查是否匹配
                if (address.equals(wallet.getCstAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.SOL.getCode();
                } else if (address.equals(wallet.getCstUsdtAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.USDT.getCode();
                } else if (address.equals(wallet.getCstUsdcAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.USDC.getCode();
                }

                if (isTargetWallet) {
                    // 重新保存地址详情到Redis
                    saveAddressDetail(address, wallet, coinType);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("快速修复地址{}失败: {}", address, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从数据库修复地址详情信息到Redis，如果数据库中不存在则清理Redis
     * 注意：这个方法现在主要用于兼容，建议使用系统重启时的批量重建机制
     *
     * @param address 需要修复的地址
     * @return 是否修复成功
     */
    public boolean repairAddressDetailFromDatabase(String address) {
        try {
            // 从数据库查询所有钱包信息
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();

            // 查找包含该地址的钱包
            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                boolean isTargetWallet = false;
                String coinType = null;

                // 检查是否是主地址
                if (address.equals(wallet.getCstAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.SOL.getCode();
                }
                // 检查是否是USDT地址
                else if (address.equals(wallet.getCstUsdtAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.USDT.getCode();
                }
                // 检查是否是USDC地址
                else if (address.equals(wallet.getCstUsdcAddress())) {
                    isTargetWallet = true;
                    coinType = SolCoinType.USDC.getCode();
                }

                if (isTargetWallet) {
                    // 重新保存地址详情到Redis
                    saveAddressDetail(address, wallet, coinType);

                    // 确保地址也在监控列表中
                    RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
                    monitorAddresses.putIfAbsent(address, SolRedisKeyConstants.STATUS_PENDING);

                    log.info("成功从数据库修复地址{}的详情信息，币种: {}", address, coinType);
                    return true;
                }
            }

            // 数据库中不存在该地址，以MySQL为准，从Redis中清理
            log.warn("数据库中未找到地址{}，以数据库为准，从Redis监控列表中移除该地址", address);
            removeAddressFromRedis(address);
            return false;

        } catch (Exception e) {
            log.error("从数据库修复地址{}详情失败: {}", address, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从Redis中完全移除地址相关信息
     *
     * @param address 要移除的地址
     */
    private void removeAddressFromRedis(String address) {
        try {
            // 从监控地址列表中移除
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
            monitorAddresses.remove(address);

            // 删除地址详情信息
            String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
            RMap<String, String> addressDetail = redissonClient.getMap(detailKey);
            if (addressDetail.isExists()) {
                addressDetail.delete();
            }

            // 从失败队列中移除（如果存在）
            RList<String> failedQueue = redissonClient.getList(SolRedisKeyConstants.SOL_MONITOR_FAILED_QUEUE);
            failedQueue.remove(address);

            // 检查并移除相关订阅映射
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            String subscriptionIdToRemove = null;
            for (Map.Entry<String, String> entry : subscriptionMapping.entrySet()) {
                if (address.equals(entry.getValue())) {
                    subscriptionIdToRemove = entry.getKey();
                    break;
                }
            }
            if (subscriptionIdToRemove != null) {
                subscriptionMapping.remove(subscriptionIdToRemove);
            }

            log.info("已从Redis中完全清理地址{}的相关信息", address);

        } catch (Exception e) {
            log.error("从Redis清理地址{}信息失败: {}", address, e.getMessage(), e);
        }
    }

    /**
     * 保存WebSocket订阅ID与地址的映射关系
     *
     * @param subscriptionId 订阅ID
     * @param address        地址
     * @return 是否保存成功
     */
    public boolean saveSubscriptionMapping(String subscriptionId, String address) {
        try {
            if (subscriptionId == null || address == null) {
                log.warn("保存订阅映射失败：订阅ID或地址为空");
                return false;
            }

            // 保存订阅ID与地址的映射关系
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            subscriptionMapping.put(subscriptionId, address);

            // 在地址详情中添加订阅ID和订阅时间
            String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
            RMap<String, String> addressDetail = redissonClient.getMap(detailKey);
            if (addressDetail.isExists()) {
                addressDetail.put("subscriptionId", subscriptionId);
                addressDetail.put("subscriptionTime", String.valueOf(System.currentTimeMillis()));
            }

            log.debug("保存订阅映射：订阅ID={}, 地址={}", subscriptionId, address);
            return true;
        } catch (Exception e) {
            log.error("保存订阅映射失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据订阅ID获取对应的地址
     *
     * @param subscriptionId 订阅ID
     * @return 地址，如果没有找到则返回null
     */
    public String getAddressBySubscription(String subscriptionId) {
        try {
            if (subscriptionId == null) {
                return null;
            }

            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            return subscriptionMapping.get(subscriptionId);
        } catch (Exception e) {
            log.error("获取订阅对应的地址失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 移除订阅ID与地址的映射关系
     *
     * @param subscriptionId 订阅ID
     * @return 是否移除成功
     */
    public boolean removeSubscriptionMapping(String subscriptionId) {
        try {
            if (subscriptionId == null) {
                return false;
            }

            // 获取映射的地址
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            String address = subscriptionMapping.remove(subscriptionId);

            // 如果找到了地址，则更新地址详情
            if (address != null) {
                String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
                RMap<String, String> addressDetail = redissonClient.getMap(detailKey);
                if (addressDetail.isExists()) {
                    addressDetail.remove("subscriptionId");
                    addressDetail.remove("subscriptionTime");
                }
            }

            return true;
        } catch (Exception e) {
            log.error("移除订阅映射失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取当前已订阅的地址集合
     * 优化版本：基于实际订阅映射而非Redis状态判断
     *
     * @return 已订阅的地址集合
     */
    public Set<String> getCurrentlySubscribedAddresses() {
        try {
            Set<String> subscribedAddresses = new HashSet<>();

            // 方法1：从订阅映射中获取实际已订阅的地址
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            subscribedAddresses.addAll(subscriptionMapping.values());

            // 方法2：作为备用，也检查Redis状态（但权重较低）
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
            Set<String> statusBasedAddresses = new HashSet<>();

            for (Map.Entry<String, String> entry : monitorAddresses.entrySet()) {
                String address = entry.getKey();
                String status = entry.getValue();

                // 只返回已确认订阅的地址
                if (SolRedisKeyConstants.STATUS_MONITORING.equals(status)) {
                    statusBasedAddresses.add(address);
                }
            }

            // 如果订阅映射为空，则回退到状态判断（兼容性）
            if (subscribedAddresses.isEmpty() && !statusBasedAddresses.isEmpty()) {
                subscribedAddresses = statusBasedAddresses;
                log.warn("订阅映射为空，回退到状态判断，可能存在数据不一致");
            }

            log.debug("当前已订阅地址数量: {} (映射方式), {} (状态方式)",
                     subscribedAddresses.size(), statusBasedAddresses.size());
            return subscribedAddresses;

        } catch (Exception e) {
            log.error("获取已订阅地址失败: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取需要重新订阅的地址集合
     * 排除最近已订阅的地址（基于时间阈值）
     *
     * @param thresholdMinutes 时间阈值（分钟）
     * @return 需要重新订阅的地址集合
     */
    public Set<String> getAddressesNeedingResubscription(int thresholdMinutes) {
        try {
            Set<String> needResubscription = new HashSet<>();
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);
            long thresholdTime = System.currentTimeMillis() - (thresholdMinutes * 60 * 1000L);

            for (String address : monitorAddresses.keySet()) {
                String detailKey = SolRedisKeyConstants.SOL_MONITOR_ADDRESS_DETAIL_PREFIX + address;
                RMap<String, String> addressDetail = redissonClient.getMap(detailKey);

                if (addressDetail.isExists()) {
                    String subscriptionTimeStr = addressDetail.get("subscriptionTime");
                    String status = monitorAddresses.get(address);

                    // 如果没有订阅时间或订阅时间超过阈值，且状态不是监控中，则需要重新订阅
                    if (subscriptionTimeStr == null ||
                        Long.parseLong(subscriptionTimeStr) < thresholdTime ||
                        !SolRedisKeyConstants.STATUS_MONITORING.equals(status)) {
                        needResubscription.add(address);
                    }
                }
            }

            log.info("需要重新订阅的地址数量: {}, 时间阈值: {}分钟", needResubscription.size(), thresholdMinutes);
            return needResubscription;

        } catch (Exception e) {
            log.error("获取需要重新订阅的地址失败: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 清理过期的订阅映射
     * 清理内存中可能残留的旧订阅ID
     */
    public void cleanupExpiredSubscriptions() {
        try {
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);

            Set<String> validAddresses = monitorAddresses.keySet();
            Set<String> subscriptionsToRemove = new HashSet<>();

            // 找出无效的订阅映射
            for (Map.Entry<String, String> entry : subscriptionMapping.entrySet()) {
                String subscriptionId = entry.getKey();
                String address = entry.getValue();

                if (!validAddresses.contains(address)) {
                    subscriptionsToRemove.add(subscriptionId);
                }
            }

            // 清理无效的订阅映射
            for (String subscriptionId : subscriptionsToRemove) {
                subscriptionMapping.remove(subscriptionId);
                log.debug("清理无效订阅映射: {}", subscriptionId);
            }

            if (!subscriptionsToRemove.isEmpty()) {
                log.info("清理了{}个无效的订阅映射", subscriptionsToRemove.size());
            }

        } catch (Exception e) {
            log.error("清理过期订阅映射失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取所有订阅映射（用于恢复内存状态）
     *
     * @return 订阅ID到地址的映射
     */
    public Map<String, String> getAllSubscriptionMappings() {
        try {
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            return new HashMap<>(subscriptionMapping);
        } catch (Exception e) {
            log.error("获取所有订阅映射失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 检查并修复订阅状态一致性
     * 确保Redis订阅状态与实际订阅映射保持一致
     *
     * @return 修复的不一致状态数量
     */
    public int checkAndRepairSubscriptionConsistency() {
        try {
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);

            Set<String> actualSubscribedAddresses = new HashSet<>(subscriptionMapping.values());
            int repairCount = 0;

            // 检查所有监控地址的状态
            for (Map.Entry<String, String> entry : monitorAddresses.entrySet()) {
                String address = entry.getKey();
                String currentStatus = entry.getValue();

                boolean shouldBeMonitoring = actualSubscribedAddresses.contains(address);
                boolean isCurrentlyMonitoring = SolRedisKeyConstants.STATUS_MONITORING.equals(currentStatus);

                if (shouldBeMonitoring && !isCurrentlyMonitoring) {
                    // 地址有订阅映射但状态不是MONITORING，修复为MONITORING
                    monitorAddresses.put(address, SolRedisKeyConstants.STATUS_MONITORING);
                    repairCount++;
                    log.debug("修复状态不一致：地址{}从{}修复为MONITORING", address, currentStatus);

                } else if (!shouldBeMonitoring && isCurrentlyMonitoring) {
                    // 地址没有订阅映射但状态是MONITORING，修复为PENDING
                    monitorAddresses.put(address, SolRedisKeyConstants.STATUS_PENDING);
                    repairCount++;
                    log.debug("修复状态不一致：地址{}从MONITORING修复为PENDING（无订阅映射）", address);
                }
            }

            if (repairCount > 0) {
                log.info("订阅状态一致性检查完成，修复了{}个不一致状态", repairCount);
            } else {
                log.debug("订阅状态一致性检查完成，未发现不一致状态");
            }

            return repairCount;

        } catch (Exception e) {
            log.error("检查订阅状态一致性失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取订阅状态统计信息
     * 用于监控和调试
     */
    public SubscriptionStatusSummary getSubscriptionStatusSummary() {
        try {
            RMap<String, String> subscriptionMapping = redissonClient.getMap(SolRedisKeyConstants.SOL_SUBSCRIPTION_MAPPING);
            RMap<String, String> monitorAddresses = redissonClient.getMap(SolRedisKeyConstants.SOL_MONITOR_ADDRESSES);

            int totalAddresses = monitorAddresses.size();
            int pendingCount = 0;
            int monitoringCount = 0;
            int failedCount = 0;
            int actualSubscribed = subscriptionMapping.size();

            for (String status : monitorAddresses.values()) {
                switch (status) {
                    case SolRedisKeyConstants.STATUS_PENDING -> pendingCount++;
                    case SolRedisKeyConstants.STATUS_MONITORING -> monitoringCount++;
                    case SolRedisKeyConstants.STATUS_FAILED -> failedCount++;
                }
            }

            return SubscriptionStatusSummary.builder()
                .totalAddresses(totalAddresses)
                .pendingCount(pendingCount)
                .monitoringCount(monitoringCount)
                .failedCount(failedCount)
                .actualSubscribedCount(actualSubscribed)
                .consistencyRatio(totalAddresses > 0 ? (double) actualSubscribed / totalAddresses : 0.0)
                .build();

        } catch (Exception e) {
            log.error("获取订阅状态统计失败: {}", e.getMessage(), e);
            return SubscriptionStatusSummary.builder().build();
        }
    }

    /**
     * 订阅状态统计摘要
     */
    @lombok.Builder
    @lombok.Data
    public static class SubscriptionStatusSummary {
        private int totalAddresses;
        private int pendingCount;
        private int monitoringCount;
        private int failedCount;
        private int actualSubscribedCount;
        private double consistencyRatio;
    }
}

