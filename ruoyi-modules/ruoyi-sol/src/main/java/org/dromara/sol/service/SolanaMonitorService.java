package org.dromara.sol.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.config.SolanaMonitorConfig;
import org.dromara.sol.constants.SolRedisKeyConstants;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.MonitorState;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.event.TransactionEvent;
import org.dromara.sol.event.WalletMonitorEvent;
import org.dromara.sol.manager.ConnectionStateManager;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.manager.SolTransactionManager;
import org.dromara.sol.manager.SubscriptionStateManager;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static org.dromara.sol.enums.SolCoinType.*;

/**
 * Solana监控服务
 * 统一管理WebSocket连接、地址订阅和交易处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SolanaMonitorService implements ApplicationRunner {

    private final SolRpcConfig rpcConfig;
    private final SolanaMonitorConfig monitorConfig;
    private final SolMonitorManager solMonitorManager;
    private final SolTransactionManager solTransactionManager;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final RedissonClient redissonClient;
    private final ApplicationEventPublisher eventPublisher;

    // 新的状态管理器
    private final ConnectionStateManager connectionStateManager;
    private final SubscriptionStateManager subscriptionStateManager;

    // WebSocket客户端
    private WebSocketClient webSocketClient;

    // 限流器
    private RRateLimiter rateLimiter;

    // JSON处理器
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 重连计数器
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0);

    // 单线程执行器（专用于重连任务）
    private ScheduledExecutorService reconnectExecutor;

    // 当前重连任务的Future（用于取消）
    private volatile ScheduledFuture<?> currentReconnectTask;

    // 最后一次全量订阅时间
    private volatile long lastFullSubscriptionTime = 0;

    // 内存中的订阅ID到地址映射（用于快速查找交易对应的地址）
    private final Map<String, String> subscriptionAddressMap = new ConcurrentHashMap<>();

    // 内存状态管理 - 新增字段 ⭐
    // 监控地址状态映射 {address: status}
    private final Map<String, String> memoryAddressStatusMap = new ConcurrentHashMap<>();

    // 地址详情映射 {address: AddressDetail}
    private final Map<String, AddressDetail> memoryAddressDetailMap = new ConcurrentHashMap<>();

    // 失败地址集合（需要重试的地址）
    private final Set<String> memoryFailedAddresses = ConcurrentHashMap.newKeySet();

    // 当前状态（使用ConnectionStateManager替代）

    /**
     * 地址详情信息（内存版本）
     */
    public static class AddressDetail {
        private final Long walletId;
        private final String address;
        private final String mainAddress;
        private final String coinType;
        private volatile String status;
        private volatile long lastUpdateTime;

        public AddressDetail(Long walletId, String address, String mainAddress, String coinType, String status) {
            this.walletId = walletId;
            this.address = address;
            this.mainAddress = mainAddress;
            this.coinType = coinType;
            this.status = status;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        // Getters
        public Long getWalletId() { return walletId; }
        public String getAddress() { return address; }
        public String getMainAddress() { return mainAddress; }
        public String getCoinType() { return coinType; }
        public String getStatus() { return status; }
        public long getLastUpdateTime() { return lastUpdateTime; }

        // Status update
        public void updateStatus(String newStatus) {
            this.status = newStatus;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        @Override
        public String toString() {
            return String.format("AddressDetail{walletId=%d, address='%s', coinType='%s', status='%s'}",
                walletId, address, coinType, status);
        }
    }

    @PostConstruct
    public void init() {
        // 初始化单线程执行器（专用于重连任务）
        reconnectExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "solana-reconnect-executor");
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });

        // 初始化限流器
        rateLimiter = redissonClient.getRateLimiter("solana:monitor:rate_limiter");
        rateLimiter.trySetRate(RateType.OVERALL, monitorConfig.getSubscribeRateLimit(),
            Duration.of(1, ChronoUnit.SECONDS));

        // 从Redis恢复订阅映射到内存
        subscriptionStateManager.syncFromRedis();

        // 启动订阅健康检查定时器
        startSubscriptionHealthCheckTimer();

        log.info("Solana监控服务初始化完成");
    }

    /**
     * 启动订阅健康检查定时器
     * 定期检查订阅状态一致性并自动修复
     */
    private void startSubscriptionHealthCheckTimer() {
        // 使用线程池定期执行健康检查（每5分钟检查一次）
        new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    TimeUnit.MINUTES.sleep(5); // 每5分钟检查一次

                    if (getCurrentState().isConnected()) {
                        performSubscriptionHealthCheck();
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("订阅健康检查执行失败: {}", e.getMessage(), e);
                }
            }
        }, "subscription-health-check").start();
    }

    /**
     * 执行订阅健康检查
     * 检查并修复订阅状态不一致的情况
     */
    private void performSubscriptionHealthCheck() {
        try {
            setTenant();

            // 1. 检查并修复订阅状态一致性
            solMonitorManager.checkAndRepairSubscriptionConsistency();

            // 2. 获取订阅状态统计信息
            SolMonitorManager.SubscriptionStatusSummary summary = solMonitorManager.getSubscriptionStatusSummary();

            // 3. 记录健康检查结果
            log.debug("订阅健康检查完成 - 总地址:{}, 监控中:{}, 待处理:{}, 失败:{}, 实际订阅:{}, 一致性:{}%",
                summary.getTotalAddresses(),
                summary.getMonitoringCount(),
                summary.getPendingCount(),
                summary.getFailedCount(),
                summary.getActualSubscribedCount(),
                summary.getConsistencyRatio() * 100);

            // 4. 如果一致性较低，触发状态同步
            if (summary.getConsistencyRatio() < 0.8 && summary.getTotalAddresses() > 0) {
                log.warn("订阅状态一致性较低({}%)，触发状态同步", summary.getConsistencyRatio() * 100);
                subscriptionStateManager.syncFromRedis();
            }

            // 5. 如果有太多失败的地址，尝试重新订阅
            if (summary.getFailedCount() > 10) {
                log.warn("失败地址过多({})，尝试重新订阅部分失败地址", summary.getFailedCount());
                retryFailedSubscriptions();
            }

        } catch (Exception e) {
            log.error("订阅健康检查失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重试失败的订阅
     * 针对失败状态的地址进行重新订阅尝试
     */
    private void retryFailedSubscriptions() {
        try {
            Set<String> failedAddresses = solMonitorManager.getAddressesByStatus(SolRedisKeyConstants.STATUS_FAILED);

            if (failedAddresses.isEmpty()) {
                return;
            }

            // 限制每次重试的数量，避免过载
            int maxRetryCount = Math.min(failedAddresses.size(), 20);
            int retryCount = 0;

            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();

            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                if (retryCount >= maxRetryCount) {
                    break;
                }

                // 检查该钱包是否有失败的地址
                boolean hasFailedAddress = failedAddresses.contains(wallet.getCstAddress()) ||
                    failedAddresses.contains(wallet.getCstUsdtAddress()) ||
                    failedAddresses.contains(wallet.getCstUsdcAddress());

                if (hasFailedAddress) {
                    log.debug("重试失败地址的订阅：钱包ID={}", wallet.getId());
                    addMonitorAddress(wallet);
                    retryCount++;
                }
            }

            log.info("完成失败地址重试订阅，处理了{}个钱包", retryCount);

        } catch (Exception e) {
            log.error("重试失败订阅失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void run(ApplicationArguments args) {
        setTenant();

        try {
            // 🔄 内存优先架构：从数据库直接初始化内存状态
            initializeMemoryStateFromDatabase();

            // 初始化WebSocket连接
            initializeConnection();

            // 等待连接建立
            if (waitForConnection()) {
                // 订阅所有监控地址（基于内存状态）
                subscribeAllAddresses();

                // 如果启用补偿，执行一次扫描
                if (monitorConfig.isEnableCompensation()) {
                    performCompensationScan();
                }
            }

        } catch (Exception e) {
            log.error("监控服务启动失败", e);
        }
    }

    /**
     * 初始化WebSocket连接
     */
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(delay = 2000))
    private void initializeConnection() {
        try {
            String websocketUrl = rpcConfig.getWebsocketUrl();
            if (websocketUrl == null || websocketUrl.trim().isEmpty()) {
                throw new IllegalStateException("WebSocket URL未配置");
            }

            connectionStateManager.setState(MonitorState.CONNECTING);

            webSocketClient = new WebSocketClient(new URI(websocketUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    handleConnectionOpen(handshake);
                }

                @Override
                public void onMessage(String message) {
                    handleMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    handleConnectionClose(code, reason, remote);
                }

                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket发生错误", ex);
                    connectionStateManager.setState(MonitorState.ERROR);
                    // 错误状态也需要触发重连
                    handleReconnectionLogic("连接错误");
                }
            };

            webSocketClient.setConnectionLostTimeout(monitorConfig.getConnectionTimeout());
            webSocketClient.connect();

            log.info("正在连接WebSocket: {}", websocketUrl);

        } catch (Exception e) {
            log.error("初始化WebSocket连接失败", e);
            connectionStateManager.setState(MonitorState.ERROR);
            throw new RuntimeException("初始化连接失败", e);
        }
    }

    /**
     * 处理连接打开事件
     */
    private void handleConnectionOpen(ServerHandshake handshake) {
        log.info("WebSocket连接已建立，HTTP状态: {}", handshake.getHttpStatus());

        // 重置重连计数器
        reconnectAttempts.set(0);

        // 清理过期的订阅映射
        solMonitorManager.cleanupExpiredSubscriptions();

        connectionStateManager.setState(MonitorState.CONNECTED);
    }

    /**
     * 处理连接关闭事件
     */
    private void handleConnectionClose(int code, String reason, boolean remote) {
        log.info("WebSocket连接关闭 [代码: {}, 原因: {}, 远程关闭: {}]", code, reason, remote);
        connectionStateManager.setState(MonitorState.DISCONNECTED);

        // 统一处理重连逻辑
        handleReconnectionLogic("连接关闭");
    }

    /**
     * 统一处理重连逻辑
     * 使用单线程执行器保证顺序执行
     */
    private void handleReconnectionLogic(String reason) {
        // 如果未启用自动重连，直接返回
        if (!monitorConfig.isEnableAutoReconnect()) {
            log.info("自动重连未启用，停止重连尝试");
            return;
        }

        int currentAttempts = reconnectAttempts.get();
        int maxAttempts = monitorConfig.getMaxReconnectAttempts();

        // 如果未超过最大重连次数，继续重连
        if (currentAttempts < maxAttempts) {
            log.info("触发重连逻辑，原因: {}, 当前尝试: {}/{}", reason, currentAttempts, maxAttempts);
            scheduleReconnect();
        } else {
            log.error("已达到最大重连次数 {}，停止自动重连", maxAttempts);
            connectionStateManager.setState(MonitorState.ERROR);

            // 启动重连计数器重置定时器（5分钟后重置）
            scheduleReconnectReset();
        }
    }

    /**
     * 安排重连计数器重置
     * 在达到最大重连次数后，等待一段时间后重置计数器，允许重新开始重连
     */
    private void scheduleReconnectReset() {
        log.info("启动重连计数器重置定时器，5分钟后重置");

        reconnectExecutor.schedule(() -> {
            try {
                int resetAttempts = reconnectAttempts.getAndSet(0);
                log.info("重连计数器已重置，原值: {}，开始新的重连周期", resetAttempts);

                // 重置后立即尝试一次重连
                if (connectionStateManager.getCurrentState().needsReconnect()) {
                    log.info("重连计数器重置后，尝试重新连接");
                    scheduleReconnect();
                }

            } catch (Exception e) {
                log.error("重连计数器重置失败: {}", e.getMessage(), e);
            }
        }, 5, TimeUnit.MINUTES);
    }

    /**
     * 处理WebSocket消息
     */
    private void handleMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(message);

            if (rootNode.has("result")) {
                // 处理订阅确认
                handleSubscriptionConfirmation(rootNode);
            } else {
                // 处理交易消息
                handleTransactionMessage(rootNode);
            }

        } catch (JsonProcessingException e) {
            log.error("解析JSON消息失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("处理消息时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 处理订阅确认消息
     */
    private void handleSubscriptionConfirmation(JsonNode rootNode) {
        try {
            String subscriptionId = rootNode.get("result").asText();
            String requestId = rootNode.get("id").asText();

            // 解析币种和钱包ID
            String coinType = null;
            String walletIdStr = null;

            for (SolCoinType solCoinType : values()) {
                String coinCode = solCoinType.getCode();
                if (requestId.startsWith(coinCode)) {
                    coinType = coinCode;
                    walletIdStr = requestId.substring(coinCode.length());
                    break;
                }
            }

            if (coinType != null) {
                long walletId = Long.parseLong(walletIdStr);
                MetaSolanaCstaddressinfoVo wallet = solanaCstaddressinfoService.queryById(walletId);

                if (wallet != null) {
                    String address = getAddressByCoinType(wallet, coinType);
                    if (address != null) {
                        // 🔄 使用内存操作更新状态为监控中
                        updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_MONITORING);

                        // 保存订阅映射到内存
                        subscriptionAddressMap.put(subscriptionId, address);

                        log.info("地址{}订阅成功，币种: {}, 订阅ID: {}", address, coinType, subscriptionId);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理订阅确认消息失败", e);
        }
    }

    /**
     * 处理交易消息
     */
    private void handleTransactionMessage(JsonNode rootNode) {
        try {
            String signature = rootNode.at("/params/result/value/signature").asText(null);
            if (signature != null && !signature.isEmpty()) {
                String subscription = rootNode.at("/params/subscription").asText(null);

                // 优先从SubscriptionStateManager获取地址映射
                String address = subscriptionStateManager.getAddressBySubscription(subscription);

                // 如果SubscriptionStateManager中没有，则从本地内存映射获取（兼容性）
                if (address == null) {
                    address = subscriptionAddressMap.get(subscription);

                    // 如果本地映射中有，同步到SubscriptionStateManager
                    if (address != null) {
                        subscriptionStateManager.saveSubscriptionMapping(subscription, address);
                        log.debug("同步订阅映射到SubscriptionStateManager: {} -> {}", subscription, address);
                    }
                }

                if (address != null) {
                    log.info("收到交易：签名={}, 地址={}, 订阅ID={}", signature, address, subscription);

                    // 发布交易事件
                    eventPublisher.publishEvent(new TransactionEvent(this, signature, address));
                } else {
                    log.warn("未找到订阅ID对应的地址：订阅ID={}, 签名={}", subscription, signature);
                }
            }
        } catch (Exception e) {
            log.error("处理交易消息失败", e);
        }
    }

    /**
     * 订阅所有监控地址（内存版本）
     * 基于内存状态进行订阅，避免Redis操作
     */
    private void subscribeAllAddresses() {
        try {
            setTenant();
            log.info("📡 开始订阅所有监控地址（内存版本）...");

            // 从内存获取所有监控地址
            Set<String> allAddresses = getAllMonitorAddressesFromMemory();
            if (allAddresses.isEmpty()) {
                log.info("📡 内存中没有找到需要监控的地址");
                setState();
                return;
            }

            log.info("📡 内存中找到{}个地址，开始逐个订阅", allAddresses.size());
            int successCount = 0;

            for (String address : allAddresses) {
                try {
                    AddressDetail detail = getAddressDetailFromMemory(address);
                    if (detail != null) {
                        SolCoinType coinType = SolCoinType.fromCode(detail.getCoinType());
                        if (subscribeAddressDirectly(address, detail.getWalletId(), coinType)) {
                            updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_PENDING);
                            successCount++;
                        } else {
                            updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_FAILED);
                        }
                    }
                } catch (Exception e) {
                    log.warn("📡 订阅地址失败: {}, 错误: {}", address, e.getMessage());
                    updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_FAILED);
                }
            }

            setState();
            log.info("📡 全量地址订阅完成，成功订阅: {}/{}", successCount, allAddresses.size());

        } catch (Exception e) {
            log.error("📡 订阅所有地址失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让调用方处理
        }
    }

    /**
     * 添加监控地址（内存版本）
     */
    public void addMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        try {
            // 🔄 使用内存操作替代Redis
            addMonitorAddressToMemory(wallet);

            if (!getCurrentState().canSendMessage()) {
                log.warn("WebSocket未连接，仅将地址添加到内存监控列表");
                return;
            }

            // 订阅各种币种地址
            Long walletId = wallet.getId();
            subscribeAddressIfExists(wallet.getCstAddress(), walletId, SOL);
            subscribeAddressIfExists(wallet.getCstUsdtAddress(), walletId, USDT);
            subscribeAddressIfExists(wallet.getCstUsdcAddress(), walletId, USDC);

        } catch (Exception e) {
            log.error("添加监控地址失败", e);
        }
    }

    /**
     * 如果地址存在则订阅（内存版本）
     */
    private void subscribeAddressIfExists(String address, Long walletId, SolCoinType coinType) {
        if (address != null && !address.isEmpty()) {
            if (!subscribeAddress(address, walletId, coinType)) {
                // 🔄 使用内存操作
                updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_FAILED);
            }
        }
    }

    /**
     * 订阅指定地址（内存版本）
     */
    public boolean subscribeAddress(String address, Long walletId, SolCoinType coinType) {
        if (!getCurrentState().canSendMessage()) {
            log.warn("WebSocket状态不允许发送消息: {}", getCurrentState());
            return false;
        }

        try {
            // 限流
            rateLimiter.acquire(1);

            // 构建订阅请求
            String request = buildSubscriptionRequest(address, walletId, coinType.getCode());
            webSocketClient.send(request);

            // 🔄 使用内存操作更新状态为待确认
            updateAddressStatusInMemory(address, SolRedisKeyConstants.STATUS_PENDING);

            log.debug("已发送地址{}的订阅请求，币种: {}", address, coinType.getCode());
            return true;

        } catch (Exception e) {
            log.error("订阅地址{}失败", address, e);
            return false;
        }
    }

    /**
     * 直接订阅地址（不检查WebSocket状态，用于重连后批量订阅）
     */
    private boolean subscribeAddressDirectly(String address, Long walletId, SolCoinType coinType) {
        try {
            // 限流
            rateLimiter.acquire(1);

            // 构建订阅请求
            String request = buildSubscriptionRequest(address, walletId, coinType.getCode());
            webSocketClient.send(request);

            log.debug("直接发送地址{}的订阅请求，币种: {}", address, coinType.getCode());
            return true;

        } catch (Exception e) {
            log.error("直接订阅地址{}失败", address, e);
            return false;
        }
    }

    /**
     * 构建订阅请求
     */
    private String buildSubscriptionRequest(String address, Long walletId, String coinType) {
        Map<String, Object> params = Map.of(
            "jsonrpc", "2.0",
            "id", coinType + walletId,
            "method", "logsSubscribe",
            "params", Arrays.asList(
                Map.of("mentions", Collections.singletonList(address)),
                Map.of("commitment", "finalized")
            )
        );
        return JsonUtils.toJsonString(params);
    }

    /**
     * 根据币种获取地址
     */
    private String getAddressByCoinType(MetaSolanaCstaddressinfoVo wallet, String coinType) {
        return switch (coinType) {
            case "SOL" -> wallet.getCstAddress();
            case "USDT" -> wallet.getCstUsdtAddress();
            case "USDC" -> wallet.getCstUsdcAddress();
            default -> null;
        };
    }

    /**
     * 等待连接建立
     */
    private boolean waitForConnection() {
        try {
            int timeout = monitorConfig.getConnectionTimeout();
            long startTime = System.currentTimeMillis();

            while (!getCurrentState().isConnected() &&
                (System.currentTimeMillis() - startTime) < timeout * 1000L) {
                TimeUnit.MILLISECONDS.sleep(100);
            }

            return getCurrentState().isConnected();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 等待连接建立（重连专用版本）
     * 支持更长的等待时间和更好的错误处理
     */
    private boolean waitForConnectionWithRetry() {
        try {
            // 重连时使用更长的超时时间（原来的2倍）
            int timeout = monitorConfig.getConnectionTimeout() * 2;
            long startTime = System.currentTimeMillis();

            log.debug("等待连接建立，超时时间: {}秒", timeout);

            while (!getCurrentState().isConnected() &&
                (System.currentTimeMillis() - startTime) < timeout * 1000L) {

                // 检查是否进入错误状态
                if (getCurrentState() == MonitorState.ERROR) {
                    log.warn("连接进入错误状态，停止等待");
                    return false;
                }

                TimeUnit.MILLISECONDS.sleep(200); // 稍微增加检查间隔
            }

            boolean connected = getCurrentState().isConnected();
            if (connected) {
                log.info("连接建立成功，当前状态: {}", getCurrentState().getDescription());
            } else {
                log.warn("等待连接建立超时，当前状态: {}，耗时: {}秒",
                    getCurrentState().getDescription(),
                    (System.currentTimeMillis() - startTime) / 1000);
            }

            return connected;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待连接建立被中断");
            return false;
        }
    }

    /**
     * 安排重连
     * 使用单线程执行器保证顺序执行
     */
    private void scheduleReconnect() {
        // 取消之前的重连任务（如果存在）
        cancelCurrentReconnectTask();

        int currentAttempt = reconnectAttempts.incrementAndGet();
        long backoffDelay = calculateBackoffDelay(currentAttempt);

        log.info("第{}次重连尝试，等待{}秒后重连...", currentAttempt, backoffDelay);

        // 使用单线程执行器提交重连任务
        currentReconnectTask = reconnectExecutor.schedule(
            () -> executeReconnectAttempt(currentAttempt),
            backoffDelay,
            TimeUnit.SECONDS
        );
    }

    /**
     * 取消当前的重连任务
     */
    private void cancelCurrentReconnectTask() {
        if (currentReconnectTask != null && !currentReconnectTask.isDone()) {
            currentReconnectTask.cancel(true);
            log.debug("取消之前的重连任务");
        }
    }

    /**
     * 计算重连退避延迟
     */
    private long calculateBackoffDelay(int currentAttempt) {
        // 指数退避：基础间隔 * 2^(重连次数-1)，最大不超过5分钟
        return Math.min(
            monitorConfig.getReconnectInterval() * (1L << (currentAttempt - 1)),
            300 // 最大5分钟
        );
    }

    /**
     * 执行单次重连尝试
     */
    private void executeReconnectAttempt(int currentAttempt) {
        try {
            log.info("开始第{}次重连尝试...", currentAttempt);

            // 清理旧的WebSocket连接
            closeOldWebSocketConnection();

            // 初始化新连接
            initializeConnection();

            // 等待连接建立并处理结果
            if (waitForConnectionWithRetry()) {
                handleReconnectSuccess();
            } else {
                handleReconnectFailure(currentAttempt);
            }

        } catch (Exception e) {
            log.error("第{}次重连尝试失败: {}", currentAttempt, e.getMessage(), e);
            handleReconnectFailure(currentAttempt);
        }
    }

    /**
     * 关闭旧的WebSocket连接
     */
    private void closeOldWebSocketConnection() {
        if (webSocketClient != null && !webSocketClient.isClosed()) {
            try {
                webSocketClient.close();
            } catch (Exception e) {
                log.debug("关闭旧WebSocket连接时出现异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 处理重连成功
     */
    private void handleReconnectSuccess() {
        log.info("重连成功，准备异步重新订阅地址...");

        try {
            // 异步执行订阅操作，避免在重连线程中执行重量级Redis操作
            handleReconnectSuccessInternal();

        } catch (Exception e) {
            log.error("提交异步订阅任务失败: {}", e.getMessage(), e);
            // 如果异步提交失败，可以考虑降级到同步执行
            // 但要添加更多的异常保护
        }
    }

    /**
     * 处理重连失败
     */
    private void handleReconnectFailure(int currentAttempt) {
        try {
            // 检查是否需要继续尝试
            if (currentAttempt < monitorConfig.getMaxReconnectAttempts()) {
                log.info("第{}次重连失败，将继续尝试下一次重连", currentAttempt);
                // 继续下一次重连尝试
                scheduleReconnect();
            } else {
                log.error("重连尝试已达到最大次数 {}，进入等待重置状态", monitorConfig.getMaxReconnectAttempts());
                // 启动重连计数器重置定时器
                scheduleReconnectReset();
            }
        } catch (Exception e) {
            log.error("处理重连后续逻辑失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行补偿扫描
     */
    private void performCompensationScan() {
        if (!monitorConfig.isEnableCompensation()) {
            return;
        }

        // 简化版补偿扫描，如果需要可以后续扩展
        log.info("执行遗漏交易补偿扫描");
        // TODO: 实现简化的补偿逻辑
    }

    /**
     * 处理钱包监控事件
     */
    @EventListener
    public void handleWalletMonitorEvent(WalletMonitorEvent event) {
        MetaSolanaCstaddressinfoVo wallet = event.getWallet();
        addMonitorAddress(wallet);
    }

    /**
     * 获取当前状态
     */
    public MonitorState getCurrentState() {
        return connectionStateManager.getCurrentState();
    }

    /**
     * 设置状态
     * 优化版本：避免不必要的重复订阅
     */
    private void setState() {
        MonitorState oldState = connectionStateManager.getCurrentState();
        connectionStateManager.setState(MonitorState.MONITORING);

        if (oldState != MonitorState.MONITORING) {
            log.info("监控状态变更: {} -> {}", oldState.getDescription(), MonitorState.MONITORING.getDescription());

            // 只在特定状态转换时触发订阅，避免重复订阅
        }
    }

    /**
     * 获取所有监控地址
     */
    public Set<String> getAllMonitorAddresses() {
        return solMonitorManager.getAllMonitorAddresses();
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return getCurrentState().isConnected();
    }

    /**
     * 重连后强制重新订阅所有地址
     * 简化版本：每次重连都全量重新订阅，确保状态一致性
     */
    private void forceResubscribeAfterReconnect() {
        try {
            setTenant();
            log.info("🔄 [重连订阅] 开始全量重新订阅所有地址...");

            // 1. 清理所有旧的订阅映射（WebSocket重连后订阅ID都失效）
            subscriptionAddressMap.clear();
            subscriptionStateManager.clearAllMappings();
            log.info("🔄 [重连订阅] 已清理旧的订阅映射");

            // 2. 简化状态管理：不再维护单个地址的订阅状态
            // 每次重连都全量重新订阅，避免状态不一致问题
            log.info("🔄 [重连订阅] 采用全量重新订阅策略，确保状态一致性");

            // 3. 执行全量重新订阅
            subscribeAllAddresses();

            log.info("🔄 [重连订阅] 全量重新订阅完成");

        } catch (Exception e) {
            log.error("🔄 [重连订阅] 全量重新订阅失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让调用方处理
        }
    }

    /**
     * 检查钱包是否需要订阅
     */
    private boolean checkIfWalletNeedsSubscription(MetaSolanaCstaddressinfoVo wallet, Set<String> currentlySubscribed) {
        // 检查主地址
        if (wallet.getCstAddress() != null && !currentlySubscribed.contains(wallet.getCstAddress())) {
            return true;
        }

        // 检查USDT地址
        if (wallet.getCstUsdtAddress() != null && !currentlySubscribed.contains(wallet.getCstUsdtAddress())) {
            return true;
        }

        // 检查USDC地址
        return wallet.getCstUsdcAddress() != null && !currentlySubscribed.contains(wallet.getCstUsdcAddress());
    }

    /**
     * 订阅需要重新订阅的地址
     */
    private void subscribeAddressesNeedingResubscription(int thresholdMinutes) {
        try {
            setTenant();
            Set<String> addressesNeedingResubscription = solMonitorManager.getAddressesNeedingResubscription(thresholdMinutes);

            if (addressesNeedingResubscription.isEmpty()) {
                log.info("没有需要重新订阅的地址");
                setState();
                return;
            }

            log.info("开始重新订阅{}个地址", addressesNeedingResubscription.size());

            // 根据地址找到对应的钱包并重新订阅
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();
            int resubscribedCount = 0;

            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {

                if (addressesNeedingResubscription.contains(wallet.getCstAddress()) ||
                    addressesNeedingResubscription.contains(wallet.getCstUsdtAddress()) ||
                    addressesNeedingResubscription.contains(wallet.getCstUsdcAddress())) {

                    addMonitorAddress(wallet);
                    resubscribedCount++;
                }
            }

            setState();
            log.info("完成增量重新订阅，处理了{}个钱包", resubscribedCount);

        } catch (Exception e) {
            log.error("增量重新订阅失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重置重连计数器（用于手动重连或其他场景）
     */
    public void resetReconnectAttempts() {
        reconnectAttempts.set(0);
        log.info("重连计数器已重置");
    }

    /**
     * 获取当前重连次数
     */
    public int getCurrentReconnectAttempts() {
        return reconnectAttempts.get();
    }

    private static void setTenant() {
        TenantHelper.setDynamic("000000", true);
    }

    @PreDestroy
    public void cleanup() {
        try {
            // 关闭WebSocket连接
            if (webSocketClient != null && webSocketClient.isOpen()) {
                webSocketClient.close();
            }

            // 取消当前的重连任务
            if (currentReconnectTask != null && !currentReconnectTask.isDone()) {
                currentReconnectTask.cancel(true);
                log.info("已取消当前重连任务");
            }

            // 关闭重连执行器
            if (reconnectExecutor != null && !reconnectExecutor.isShutdown()) {
                reconnectExecutor.shutdown();
                try {
                    // 等待最多10秒让任务完成
                    if (!reconnectExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.warn("重连执行器未能在10秒内正常关闭，强制关闭");
                        reconnectExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    log.warn("等待重连执行器关闭时被中断");
                    reconnectExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            // 清理状态
            reconnectAttempts.set(0);

            log.info("SolanaMonitorService清理完成");
        } catch (Exception e) {
            log.error("清理过程中发生错误", e);
        }
    }

    public SolTransactionManager getSolTransactionManager() {
        return solTransactionManager;
    }

    /**
     * 处理重连成功（同步版本）
     * 确保连接真正稳定后再执行订阅，避免时序问题
     */
    private void handleReconnectSuccessInternal() {
        log.info("重连成功，开始同步执行订阅操作...");

        try {
            // 智能等待连接真正稳定
            if (waitForConnectionStable()) {
                log.info("连接已稳定，开始执行重连后强制重新订阅...");

                // 同步执行订阅操作，确保状态一致性
                forceResubscribeAfterReconnect();

                log.info("重连后强制重新订阅完成，连接状态: {}", getCurrentState().getDescription());
            } else {
                log.warn("等待连接稳定超时，跳过本次订阅操作");
                // 连接不稳定时，不执行订阅，让下次重连周期处理
            }

        } catch (Exception e) {
            log.error("重连后同步订阅失败: {}", e.getMessage(), e);
            // 订阅失败不影响连接状态，让重连机制继续工作
        }
    }

    /**
     * 智能等待连接真正稳定
     * 解决时序问题：确保连接不仅建立，而且可以正常使用
     */
    private boolean waitForConnectionStable() {
        final int maxRetries = 20; // 最多重试20次
        final long retryInterval = 500; // 每次等待500ms
        final long totalTimeout = maxRetries * retryInterval; // 总超时10秒

        log.debug("开始等待连接稳定，最大等待时间: {}秒", totalTimeout / 1000);

        for (int i = 0; i < maxRetries; i++) {
            try {
                // 检查连接是否真正可用
                if (isConnectionReallyStable()) {
                    log.debug("连接已稳定，重试次数: {}/{}", i + 1, maxRetries);
                    return true;
                }

                // 检查是否进入错误状态
                if (getCurrentState() == MonitorState.ERROR) {
                    log.warn("连接进入错误状态，停止等待");
                    return false;
                }

                // 等待后重试
                Thread.sleep(retryInterval);

                if (i % 4 == 3) { // 每2秒输出一次进度
                    log.debug("等待连接稳定中... ({}/{})", i + 1, maxRetries);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待连接稳定被中断");
                return false;
            }
        }

        log.warn("等待连接稳定超时，总耗时: {}秒", totalTimeout / 1000);
        return false;
    }

    /**
     * 检查连接是否真正稳定可用
     * 不仅检查状态，还验证实际可用性
     */
    private boolean isConnectionReallyStable() {
        // 1. 基础状态检查
        if (!getCurrentState().canSendMessage()) {
            return false;
        }

        // 2. WebSocket客户端状态检查
        if (webSocketClient == null || !webSocketClient.isOpen()) {
            return false;
        }

        // 3. 连接最近是否有异常
        if (connectionStateManager.getConsecutiveFailures() > 0) {
            return false;
        }

        // 4. 可选：发送心跳验证（暂时跳过，避免干扰）
        // 所有检查通过，认为连接稳定
        return true;
    }

    // ============ 内存状态管理方法 ⭐ ============

    /**
     * 添加监控地址到内存（内存版本）
     * 替代Redis操作，避免线程中断问题
     */
    private boolean addMonitorAddressToMemory(MetaSolanaCstaddressinfoVo wallet) {
        try {
            String mainAddress = wallet.getCstAddress();
            String usdtAddress = wallet.getCstUsdtAddress();
            String usdcAddress = wallet.getCstUsdcAddress();
            Long walletId = wallet.getId();

            // 添加主地址
            if (mainAddress != null && !mainAddress.isEmpty()) {
                if (!memoryAddressStatusMap.containsKey(mainAddress)) {
                    memoryAddressStatusMap.put(mainAddress, SolRedisKeyConstants.STATUS_PENDING);
                    memoryAddressDetailMap.put(mainAddress,
                        new AddressDetail(walletId, mainAddress, mainAddress, SolCoinType.SOL.getCode(), SolRedisKeyConstants.STATUS_PENDING));
                    log.debug("💾 内存添加主地址: {}", mainAddress);
                }
            }

            // 添加USDT地址
            if (usdtAddress != null && !usdtAddress.isEmpty()) {
                if (!memoryAddressStatusMap.containsKey(usdtAddress)) {
                    memoryAddressStatusMap.put(usdtAddress, SolRedisKeyConstants.STATUS_PENDING);
                    memoryAddressDetailMap.put(usdtAddress,
                        new AddressDetail(walletId, usdtAddress, mainAddress, SolCoinType.USDT.getCode(), SolRedisKeyConstants.STATUS_PENDING));
                    log.debug("💾 内存添加USDT地址: {}", usdtAddress);
                }
            }

            // 添加USDC地址
            if (usdcAddress != null && !usdcAddress.isEmpty()) {
                if (!memoryAddressStatusMap.containsKey(usdcAddress)) {
                    memoryAddressStatusMap.put(usdcAddress, SolRedisKeyConstants.STATUS_PENDING);
                    memoryAddressDetailMap.put(usdcAddress,
                        new AddressDetail(walletId, usdcAddress, mainAddress, SolCoinType.USDC.getCode(), SolRedisKeyConstants.STATUS_PENDING));
                    log.debug("💾 内存添加USDC地址: {}", usdcAddress);
                }
            }

            return true;

        } catch (Exception e) {
            log.error("💾 内存添加监控地址失败，钱包ID: {}: {}", wallet.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新地址状态（内存版本）
     */
    private boolean updateAddressStatusInMemory(String address, String status) {
        try {
            // 更新状态映射
            memoryAddressStatusMap.put(address, status);

            // 更新详情信息
            AddressDetail detail = memoryAddressDetailMap.get(address);
            if (detail != null) {
                detail.updateStatus(status);
            }

            // 管理失败地址集合
            if (SolRedisKeyConstants.STATUS_FAILED.equals(status)) {
                memoryFailedAddresses.add(address);
            } else {
                memoryFailedAddresses.remove(address);
            }

            log.debug("💾 内存状态更新: {} -> {}", address, status);
            return true;

        } catch (Exception e) {
            log.error("💾 内存状态更新失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有监控地址（内存版本）
     */
    private Set<String> getAllMonitorAddressesFromMemory() {
        return new HashSet<>(memoryAddressStatusMap.keySet());
    }

    /**
     * 根据状态获取地址集合（内存版本）
     */
    private Set<String> getAddressesByStatusFromMemory(String status) {
        return memoryAddressStatusMap.entrySet().stream()
            .filter(entry -> status.equals(entry.getValue()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }

    /**
     * 获取地址详情（内存版本）
     */
    private AddressDetail getAddressDetailFromMemory(String address) {
        return memoryAddressDetailMap.get(address);
    }

    /**
     * 清空内存状态（重连时使用）
     */
    private void clearMemoryState() {
        log.info("💾 清空内存状态，准备重新订阅");
        memoryAddressStatusMap.clear();
        memoryFailedAddresses.clear();
        // 注意：保留memoryAddressDetailMap，避免丢失地址详情
        // 只更新状态为PENDING，准备重新订阅
        memoryAddressDetailMap.values().forEach(detail -> detail.updateStatus(SolRedisKeyConstants.STATUS_PENDING));
        memoryAddressDetailMap.keySet().forEach(address -> memoryAddressStatusMap.put(address, SolRedisKeyConstants.STATUS_PENDING));
    }

    /**
     * 初始化内存状态（启动时从数据库加载）
     */
    private void initializeMemoryStateFromDatabase() {
        try {
            log.info("💾 从数据库初始化内存状态...");
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();

            int totalAddresses = 0;
            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                if (addMonitorAddressToMemory(wallet)) {
                    totalAddresses += countWalletAddresses(wallet);
                }
            }

            log.info("💾 内存状态初始化完成，共加载{}个地址", totalAddresses);

        } catch (Exception e) {
            log.error("💾 内存状态初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算钱包包含的地址数量
     */
    private int countWalletAddresses(MetaSolanaCstaddressinfoVo wallet) {
        int count = 0;
        if (wallet.getCstAddress() != null && !wallet.getCstAddress().isEmpty()) count++;
        if (wallet.getCstUsdtAddress() != null && !wallet.getCstUsdtAddress().isEmpty()) count++;
        if (wallet.getCstUsdcAddress() != null && !wallet.getCstUsdcAddress().isEmpty()) count++;
        return count;
    }
}
