package org.dromara.sol.manager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.enums.MonitorState;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 连接状态管理器
 * 采用混合策略：内存管理连接状态，Redis管理持久化数据
 * <p>
 * 设计原则：
 * 1. 连接状态（瞬时）-> 内存管理，快速响应
 * 2. 订阅映射（需恢复）-> Redis管理，支持故障恢复
 * 3. 监控配置（持久）-> 数据库管理，权威数据源
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConnectionStateManager {

    // 当前连接状态（内存管理）
    private final AtomicReference<MonitorState> currentState = new AtomicReference<>(MonitorState.DISCONNECTED);

    // 连接状态变更历史（内存管理，用于调试）
    private final ConcurrentHashMap<LocalDateTime, MonitorState> stateHistory = new ConcurrentHashMap<>();

        // 连接健康度指标（内存管理）
    private volatile long lastHeartbeatTime = 0;
    private volatile long connectionStartTime = 0;
    private volatile int consecutiveFailures = 0;

    /**
     * 设置连接状态
     * 只在内存中管理，提供最快的响应速度
     */
    public void setState(MonitorState newState) {
        MonitorState oldState = currentState.getAndSet(newState);

        if (oldState != newState) {
            // 记录状态变更历史（限制大小避免内存泄漏）
            LocalDateTime now = LocalDateTime.now();
            stateHistory.put(now, newState);

            // 保持历史记录在合理范围内（最近100条）
            if (stateHistory.size() > 100) {
                stateHistory.keySet().stream().min(LocalDateTime::compareTo).ifPresent(stateHistory::remove);
            }

            // 更新连接指标
            updateConnectionMetrics(newState);

            log.info("连接状态变更: {} -> {}",
                oldState != null ? oldState.getDescription() : "null",
                newState.getDescription());
        }
    }

    /**
     * 获取当前连接状态
     */
    public MonitorState getCurrentState() {
        return currentState.get();
    }

    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        return getCurrentState().isConnected();
    }

    /**
     * 检查是否可以发送消息
     */
    public boolean canSendMessage() {
        return getCurrentState().canSendMessage();
    }

    /**
     * 检查是否需要重连
     */
    public boolean needsReconnect() {
        return getCurrentState().needsReconnect();
    }

    /**
     * 更新心跳时间
     */
    public void updateHeartbeat() {
        this.lastHeartbeatTime = System.currentTimeMillis();
    }

    /**
     * 检查连接健康度
     *
     * @param heartbeatTimeoutMs 心跳超时时间（毫秒）
     * @return 连接是否健康
     */
    public boolean isConnectionHealthy(long heartbeatTimeoutMs) {
        if (!isConnected()) {
            return false;
        }

        long timeSinceLastHeartbeat = System.currentTimeMillis() - lastHeartbeatTime;
        return timeSinceLastHeartbeat <= heartbeatTimeoutMs;
    }

    /**
     * 记录连接失败
     */
    public void recordConnectionFailure() {
        consecutiveFailures++;
        log.warn("连接失败次数: {}", consecutiveFailures);
    }

    /**
     * 重置连接失败计数
     */
    public void resetConnectionFailures() {
        if (consecutiveFailures > 0) {
            log.info("重置连接失败计数，之前失败次数: {}", consecutiveFailures);
            consecutiveFailures = 0;
        }
    }

    /**
     * 获取连续失败次数
     */
    public int getConsecutiveFailures() {
        return consecutiveFailures;
    }

    /**
     * 获取连接持续时间（毫秒）
     */
    public long getConnectionDuration() {
        if (connectionStartTime == 0 || !isConnected()) {
            return 0;
        }
        return System.currentTimeMillis() - connectionStartTime;
    }

    /**
     * 设置重连状态
     */
    public void setReconnecting(boolean reconnecting) {
        this.reconnecting = reconnecting;
        if (reconnecting) {
            log.debug("设置重连状态: 开始重连");
        } else {
            log.debug("设置重连状态: 重连结束");
        }
    }

    /**
     * 检查是否正在重连
     */
    public boolean isReconnecting() {
        return reconnecting;
    }

    /**
     * 获取连接状态摘要
     */
    public ConnectionStateSummary getStateSummary() {
        return ConnectionStateSummary.builder()
            .currentState(getCurrentState())
            .isConnected(isConnected())
            .canSendMessage(canSendMessage())
            .consecutiveFailures(consecutiveFailures)
            .connectionDuration(getConnectionDuration())
            .lastHeartbeatTime(lastHeartbeatTime)
            .stateHistorySize(stateHistory.size())
            .reconnecting(reconnecting)
            .build();
    }

    /**
     * 更新连接指标
     */
    private void updateConnectionMetrics(MonitorState newState) {
        switch (newState) {
            case CONNECTED:
            case MONITORING:
                if (connectionStartTime == 0) {
                    connectionStartTime = System.currentTimeMillis();
                }
                resetConnectionFailures();
                updateHeartbeat();
                break;

            case DISCONNECTED:
            case ERROR:
                connectionStartTime = 0;
                recordConnectionFailure();
                break;

            case CONNECTING:
                // 连接中状态不更新指标
                break;
        }
    }

    /**
     * 连接状态摘要
     */
    @lombok.Builder
    @lombok.Data
    public static class ConnectionStateSummary {
        private MonitorState currentState;
        private boolean isConnected;
        private boolean canSendMessage;
        private int consecutiveFailures;
        private long connectionDuration;
        private long lastHeartbeatTime;
        private int stateHistorySize;
        private boolean reconnecting;
    }
}
