package org.dromara.sol.service;

import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.config.SolanaMonitorConfig;
import org.dromara.sol.enums.MonitorState;
import org.dromara.sol.manager.ConnectionStateManager;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.manager.SolTransactionManager;
import org.dromara.sol.manager.SubscriptionStateManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SolanaMonitorService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class SolanaMonitorServiceTest {

    @Mock
    private SolRpcConfig rpcConfig;

    @Mock
    private SolanaMonitorConfig monitorConfig;

    @Mock
    private SolMonitorManager solMonitorManager;

    @Mock
    private SolTransactionManager solTransactionManager;

    @Mock
    private IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    private SolanaMonitorService solanaMonitorService;

    @BeforeEach
    void setUp() {
        solanaMonitorService = new SolanaMonitorService(
            rpcConfig,
            monitorConfig,
            solMonitorManager,
            solTransactionManager,
            solanaCstaddressinfoService,
            redissonClient,
            eventPublisher
        );
    }

    @Test
    void testInitialState() {
        // 验证初始状态
        assertEquals(MonitorState.DISCONNECTED, solanaMonitorService.getCurrentState());
        assertFalse(solanaMonitorService.isConnected());
    }

    @Test
    void testStateTransitions() {
        // 测试状态枚举的各种状态
        assertTrue(MonitorState.CONNECTED.isConnected());
        assertTrue(MonitorState.MONITORING.isConnected());
        assertFalse(MonitorState.DISCONNECTED.isConnected());
        assertFalse(MonitorState.ERROR.isConnected());

        assertTrue(MonitorState.CONNECTED.canSendMessage());
        assertTrue(MonitorState.MONITORING.canSendMessage());
        assertFalse(MonitorState.DISCONNECTED.canSendMessage());

        assertTrue(MonitorState.DISCONNECTED.needsReconnect());
        assertTrue(MonitorState.ERROR.needsReconnect());
        assertFalse(MonitorState.CONNECTED.needsReconnect());
    }

    @Test
    void testReconnectAttempts() {
        // 测试重连次数管理
        assertEquals(0, solanaMonitorService.getCurrentReconnectAttempts());

        // 重置重连计数器
        solanaMonitorService.resetReconnectAttempts();
        assertEquals(0, solanaMonitorService.getCurrentReconnectAttempts());
    }

    @Test
    void testDuplicateSubscriptionPrevention() {
        // 测试防重复订阅机制
        // 这里可以添加更多具体的测试逻辑
        assertNotNull(solanaMonitorService);
    }

    @Test
    void testConfigurationDefaults() {
        // 测试配置默认值
        SolanaMonitorConfig config = new SolanaMonitorConfig();
        assertEquals(30, config.getConnectionTimeout());
        assertEquals(5, config.getMaxReconnectAttempts());
        assertEquals(10, config.getReconnectInterval());
        assertEquals(10, config.getSubscribeRateLimit());
        assertTrue(config.isEnableCompensation());
        assertEquals(60, config.getCompensationInterval());
        assertTrue(config.isEnableAutoReconnect());
        assertEquals(30, config.getHeartbeatInterval());
    }

    @Test
    void testGetAllMonitorAddresses() {
        // 配置mock行为
        when(solMonitorManager.getAllMonitorAddresses()).thenReturn(java.util.Set.of("address1", "address2"));

        // 执行测试
        var addresses = solanaMonitorService.getAllMonitorAddresses();

        // 验证结果
        assertEquals(2, addresses.size());
        assertTrue(addresses.contains("address1"));
        assertTrue(addresses.contains("address2"));
    }
}
